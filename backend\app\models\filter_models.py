from datetime import date
from typing import Optional,List, Union
from pydantic import BaseModel, Field

# -------------------- Option Level --------------------
class IllustrationOptionOut(BaseModel):
    option_id: int
    selected: bool = False
    option_display: str


# -------------------- Question Level --------------------
class IllustrationQuestionOut(BaseModel):
    question_id: int
    question_display: str
    options: List[IllustrationOptionOut]


# -------------------- Illustration Type Level --------------------
class IllustrationTypeOut(BaseModel):
    type_id: int
    type_display: str
    questions: List[IllustrationQuestionOut]


# -------------------- Full Illustration Tree Output --------------------
class FullIllustrationOptionsResponse(BaseModel):
    illustration_options: List[IllustrationTypeOut]

class Rider(BaseModel):
    RIDER_ID: int
    POLICY_ID: int
    RIDER_NAME: str
    SUM_ASSURED: float
    PREMIUM_AMOUNT: float

class Beneficiary(BaseModel):
    BENEFICIARY_ID: int
    POLICY_ID: int
    NAME: str
    RELATIONSHIP: str
    SHARE_PERCENTAGE: float

class Transaction(BaseModel):
    TRANSACTION_ID: int
    POLICY_ID: int
    TRANSACTION_TYPE: str
    TRANSACTION_DATE: str
    AMOUNT: float

class ScheduleRepayment(BaseModel):
    REPAYMENT_ID: int
    LOAN_ID: int
    DUE_DATE: str
    AMOUNT_DUE: float
    STATUS: str

class Loan(BaseModel):
    LOAN_ID: int
    POLICY_ID: int
    LOAN_AMOUNT: float
    INTEREST_RATE: float
    START_DATE: str
    repayments: Optional[List[ScheduleRepayment]] = []

class PolicyViewResponse(BaseModel):
    POLICY_ID: int
    CUSTOMER_ID: int
    INSURANCE_COMPANY_ID: int
    AGENT_ID: int
    PRODUCT_CODE: int
    POLICY_NUMBER: str
    POLICY_START_DATE: str
    POLICY_END_DATE: str
    POLICY_STATUS: str
    PREMIUM_AMOUNT: float
    FACE_AMOUNT: float
    riders: Optional[List[Rider]] = []
    beneficiaries: Optional[List[Beneficiary]] = []
    transactions: Optional[List[Transaction]] = []
    loans: Optional[List[Loan]] = []

# -------------------- Combined Output for /illustration-options/{policy_id} --------------------
class IllustrationOptionsWithPolicyResponse(BaseModel):
    policy_id: int
    insurance_product_code: str
    filtered_tree: dict  # From options_applicable_for_product
    all_illustrations: Optional[FullIllustrationOptionsResponse] = None
    policy_details: Optional[PolicyViewResponse] = None