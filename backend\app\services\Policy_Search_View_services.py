from fastapi import HTTPException
from app.models.policy_Search_View_models import PolicySearchRequest, PolicyBasicDetail
from app.db.connection import get_connection
from typing import List, Optional


def search_policy_insurer(search_keys: dict) -> Optional[List[PolicyBasicDetail]]:
    conn = None
    cursor = None

    # ✅ Reject empty requests
    if not any([
        search_keys.get("policy_id"),
        search_keys.get("customer_id"),
        search_keys.get("customer_name")
    ]):
        raise HTTPException(
            status_code=400,
            detail="At least one search parameter (policy_id, customer_id, customer_name) must be provided."
        )
    try:
        conn = get_connection()
        cursor = conn.cursor(dictionary=True)

        conditions = []
        values = []

        if search_keys.get("policy_id"):
            conditions.append("p.POLICY_ID = %s")
            values.append(search_keys["policy_id"])

        if search_keys.get("customer_id"):
            conditions.append("c.CUSTOMER_ID = %s")
            values.append(search_keys["customer_id"])

        if search_keys.get("customer_name"):
            conditions.append("LOWER(c.CUSTOMER_FIRST_NAME) LIKE %s")
            values.append(f"%{search_keys['customer_name'].lower()}%")

        # Build WHERE clause safely
        where_clause = " AND ".join(conditions)

        query = f"""
            SELECT 
                p.POLICY_ID,
                p.POLICY_TYPE,
                p.POLICY_STATUS,
                p.POLICY_EXPIRY_DATE,
                p.FACE_AMOUNT,
                c.CUSTOMER_ID,
                CONCAT(c.CUSTOMER_FIRST_NAME, ' ', c.CUSTOMER_LAST_NAME) AS CUSTOMER_NAME,
                c.CONTACT_NUMBER
            FROM INS_POLICY p
            JOIN INS_CUSTOMER c ON p.CUSTOMER_ID = c.CUSTOMER_ID
            WHERE {where_clause}
        """

        cursor.execute(query, values)
        rows = cursor.fetchall()

        if not rows:
            return None

        response_data = []
        for row in rows:
            if not row.get("CUSTOMER_NAME"):
                continue  # Skip rows where customer name is missing

            policy_detail = PolicyBasicDetail(
                policyId=row["POLICY_ID"],
                customerId=row["CUSTOMER_ID"],
                customer_name=row["CUSTOMER_NAME"],  # Ensure model has this field
                policyType=row["POLICY_TYPE"],
                status=row["POLICY_STATUS"],
                maturityDate=row["POLICY_EXPIRY_DATE"],
                faceAmount=row["FACE_AMOUNT"]
            )

            response_data.append(policy_detail)

        return response_data if response_data else None

    except Exception as e:
        print("[search_policy_insurer] DB Error:", e)
        raise HTTPException(status_code=500, detail="Database error")

    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
