from fastapi import APIRouter, HTTPException
from app.services.filter_services import get_policy_details, options_applicable_for_product

router = APIRouter()

@router.get("/illustration-options/{policy_id}")
def get_illustration_options_with_policy(policy_id: int):
    try:
        # Step 1: Fetch policy details
        policy = get_policy_details(policy_id)
        if not policy:
            raise HTTPException(status_code=404, detail="Policy not found")

        # Step 2: Extract product code
        product_code = policy.get("INSURANCE_PRODUCT_CODE")
        if not product_code:
            raise HTTPException(status_code=400, detail="Missing product code in policy")

        # Step 3: Apply final filtering logic
        filtered_tree = options_applicable_for_product(product_code, policy)  # this internally calls filter_policy_conditions

        # Step 4: Return final combined output
        return {
            "policy_details": policy,
            "filtered_tree": filtered_tree
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")
