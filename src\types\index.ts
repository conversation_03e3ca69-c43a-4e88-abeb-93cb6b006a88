export interface User {
  id: string;
  username: string;
  name: string;
  email: string;
}

export interface Policy {
  id: string;
  policyNumber: string;
  customerName: string;
  customerId: string;
  policyType: string;
  status: 'active' | 'inactive' | 'pending';
  faceAmount: number;
  premium: number;
  createdAt: Date;
}

export interface Scenario {
  id: string;
  name: string;
  policyId: string;
  asIsDetails: string;
  whatIfOptions: string[];
  category: 'face-amount' | 'premium' | 'income' | 'loan-repayment' | 'interest-rate' | 'as-is' | 'policy-lapse';
  keyPoints?: string[];
  data: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  rememberMe: boolean;
}

export interface SelectedCustomerData {
  name: string;
  policyNumber: string;
  customerId: string;
  details: {
    DOB: string;
    Email: string;
    Phone: string;
    Address: string;
    Occupation: string;
    "Annual Income": string;
    "Customer ID": string;
    "Policy Number": string;
    "Policy Type": string;
    Status: string;
  };
}

export interface SelectedPolicyData {
  id: string;
  name: string;
  description: string;
  coverage: string;
  premium: string;
  features: string[];
}

export interface PolicySearchFormData {
  customerId: string;
  policyNumber: string;
  customerName: string;
}

export interface DashboardState {
  activeTab: string;
  currentPolicy: Policy | null;
  scenarios: Scenario[];
  selectedScenarios: string[];
  selectedCustomerData: SelectedCustomerData | null;
  selectedPolicyData: SelectedPolicyData | null;
  policySearchFormData: PolicySearchFormData | null;
  loading: boolean;
  error: string | null;
  allowedIllustrationTypes: number[]; // Array of allowed illustration type IDs (1-6)
}