from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from typing import List, Union, Dict
import httpx

from app.models.policy_Search_View_models import (
    PolicySearchRequest,
    PolicyBasicDetail,
    PolicyBasicDetailsResponse,
    ErrorResponse
)
from app.services.Policy_Search_View_services import search_policy_insurer

router = APIRouter()

# 🚀 UI API Endpoint (calls insurer internally)
@router.post("/api/policy/search/ui", response_model=Union[PolicyBasicDetailsResponse, ErrorResponse])
async def search_policy_controller(request: PolicySearchRequest):
    # Filter out empty/None values
    filtered_keys = {
        k: v for k, v in request.model_dump().items()
        if v not in (None, "", 0)
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:8000/api/policy/search/insurer",
                json=filtered_keys
            )

        # ⚠️ Handle "not found" case
        if response.status_code == 404:
            return JSONResponse(
                status_code=404,
                content={
                    "errorCode": "POLICY_NOT_FOUND",
                    "message": "No policy found matching the provided details."
                }
            )

        # ⚠️ Handle other errors from internal API
        if response.status_code != 200:
            try:
                detail = response.json().get("detail", "Internal error")
            except Exception:
                detail = "Unknown error"
            return JSONResponse(
                status_code=500,
                content={
                    "errorCode": "INTERNAL_SERVER_ERROR",
                    "message": str(detail)
                }
            )

        # ✅ SUCCESS: Return the result under POLICY_DETAILS
        result = response.json()
        return {"POLICY_DETAILS": result}

    except httpx.RequestError as e:
        raise HTTPException(status_code=500, detail=f"Internal API call failed: {e}")


# 🎯 Insurer Side Endpoint (Actual logic)
@router.post("/api/policy/search/insurer", response_model=List[PolicyBasicDetail])
def customer_policy_search_api(request: PolicySearchRequest):
    search_keys = {
        "policy_id": request.policy_id,
        "customer_id": request.customer_id,
        "customer_name": request.customer_name
    }

    result = search_policy_insurer(search_keys)

    if not result:
        raise HTTPException(status_code=404, detail="No policies found for the given search keys")
    
    return result
