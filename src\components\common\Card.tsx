import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
}

const Card: React.FC<CardProps> = ({ children, className = '', padding = 'md' }) => {
  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  return (
    <div className={`bg-white dark:bg-gray-900 rounded-lg shadow-sm dark:shadow-lg border border-gray-200 dark:border-gray-800 transition-all duration-300 ${paddingClasses[padding]} ${className}`}>
      {children}
    </div>
  );
};

export default Card;