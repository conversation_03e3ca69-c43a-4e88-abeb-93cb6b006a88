import React, { useState, useEffect } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import { Save, RotateCcw } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

const InterestRatePage: React.FC = () => {
  const { selectedCustomerData, selectedPolicyData, setActiveTab } = useDashboard();

  // Scenario state
  const [stressRate, setStressRate] = useState('');
  const [stressRateEnabled, setStressRateEnabled] = useState(false);

  // State for user-defined rates functionality (following Face Amount page pattern)
  const [userDefinedRatesData, setUserDefinedRatesData] = useState({
    enabled: false,
    selectedTypes: {
      current: false,
      guaranteed: false,
      age: false,
      policyYear: false,
      calendarYear: false
    },
    ageRange: {
      start: 40,
      end: 100
    },
    policyYearRange: {
      start: 1,
      end: 100
    },
    calendarYearRange: {
      start: 2024,
      end: 2100
    },
    isEditing: false,
    tableData: [] as TableRowData[]
  });

  type TableRowData = {
    age: number;
    policyYear: string;
    calendarYear: number;
    interestRate: number;
  };

  // Get current interest rate from policy data
  const getCurrentInterestRate = (): string => {
    const policyData = selectedPolicyData as any;
    if (policyData?.CURRENT_INTEREST_RATE_IN_PERCENTAGE) {
      return `${policyData.CURRENT_INTEREST_RATE_IN_PERCENTAGE}%`;
    }
    return 'N/A';
  };

  // Get guaranteed minimum rate from policy data
  const getGuaranteedMinimumRate = (): string => {
    const policyData = selectedPolicyData as any;
    if (policyData?.GUARANTEED_INTEREST_RATE_IN_PERCENTAGE) {
      return `${policyData.GUARANTEED_INTEREST_RATE_IN_PERCENTAGE}%`;
    }
    return 'N/A';
  };

  // Calculate current age from DOB
  const calculateCurrentAge = (): number => {
    if (!selectedCustomerData?.details?.DOB) return 40;

    const dob = selectedCustomerData.details.DOB;
    let birthDate: Date;

    if (dob.includes('.')) {
      const dobParts = dob.split('.');
      if (dobParts.length !== 3) return 40;
      const [day, month, year] = dobParts.map(Number);
      birthDate = new Date(year, month - 1, day);
    } else if (dob.includes('/')) {
      const dobParts = dob.split('/');
      if (dobParts.length !== 3) return 40;
      const [first, second, year] = dobParts.map(Number);
      birthDate = new Date(year, first - 1, second);
    } else if (dob.includes('-')) {
      birthDate = new Date(dob);
    } else {
      return 40;
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return Math.max(0, age);
  };

  // Calculate current policy year from issue date
  const calculateCurrentPolicyYear = (): number => {
    const issueDate = (selectedPolicyData as any)?.issueDate || (selectedPolicyData as any)?.policyStartDate;
    if (issueDate) {
      const issue = new Date(issueDate);
      const today = new Date();
      const yearsDiff = today.getFullYear() - issue.getFullYear();
      const monthsDiff = today.getMonth() - issue.getMonth();
      const daysDiff = today.getDate() - issue.getDate();

      let totalMonths = yearsDiff * 12 + monthsDiff;
      if (daysDiff >= 0) {
        totalMonths += 1;
      }

      const policyYear = Math.max(1, Math.ceil(totalMonths / 12));
      return policyYear;
    }
    return 1;
  };

  // Get current calendar year
  const getCurrentYear = (): number => {
    return new Date().getFullYear();
  };

  // Initialize ranges with actual values
  useEffect(() => {
    const currentAge = calculateCurrentAge();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const currentYear = getCurrentYear();

    setUserDefinedRatesData(prev => ({
      ...prev,
      ageRange: {
        start: currentAge,
        end: 100
      },
      policyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      calendarYearRange: {
        start: currentYear,
        end: 2100
      }
    }));
  }, [selectedCustomerData, selectedPolicyData]);

  // Generate table data based on selected types and ranges
  const generateTableData = (): TableRowData[] => {
    const { selectedTypes, ageRange, policyYearRange, calendarYearRange } = userDefinedRatesData;

    let startYear = 0;
    let endYear = 0;

    if (selectedTypes.age) {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (ageRange.start - currentAge);
      endYear = currentCalendarYear + (ageRange.end - currentAge);
    } else if (selectedTypes.policyYear) {
      const currentPolicyYear = calculateCurrentPolicyYear();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (policyYearRange.start - currentPolicyYear);
      endYear = currentCalendarYear + (policyYearRange.end - currentPolicyYear);
    } else if (selectedTypes.calendarYear) {
      startYear = calendarYearRange.start;
      endYear = calendarYearRange.end;
    }

    if (startYear === 0 || endYear === 0 || startYear > endYear) return [];

    const currentAge = calculateCurrentAge();
    const currentCalendarYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();

    const data: TableRowData[] = [];
    const maxEntries = 12;
    const totalYears = endYear - startYear + 1;
    const actualEndYear = totalYears > maxEntries ? startYear + maxEntries - 1 : endYear;

    for (let year = startYear; year <= actualEndYear; year++) {
      const row: TableRowData = {
        age: currentAge + (year - currentCalendarYear),
        policyYear: `Year ${currentPolicyYear + (year - currentCalendarYear)}`,
        calendarYear: year,
        interestRate: 5.0 // Default interest rate
      };

      data.push(row);
    }

    return data;
  };

  // Update table data when selections change
  useEffect(() => {
    const newTableData = generateTableData();
    setUserDefinedRatesData(prev => ({ ...prev, tableData: newTableData }));
  }, [userDefinedRatesData.selectedTypes, userDefinedRatesData.ageRange, userDefinedRatesData.policyYearRange, userDefinedRatesData.calendarYearRange]);

  // Reset all scenario state
  const handleResetScenarios = () => {
    setStressRate('');
    setStressRateEnabled(false);
    setUserDefinedRatesData(prev => ({
      ...prev,
      enabled: false,
      selectedTypes: {
        current: false,
        guaranteed: false,
        age: false,
        policyYear: false,
        calendarYear: false
      },
      isEditing: false,
      tableData: []
    }));
    alert('All interest rate scenarios have been reset!');
  };

  return (
    <div className="space-y-6">

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Interest Rate illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* Description Section - match AsIsPage style */}
          <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
            <div className="p-6">
              <p className="text-lg text-gray-800 dark:text-gray-200 leading-relaxed">
                These scenarios show how your policy performs under different interest rate environments—typically Current, Guaranteed, and Alternative Rates. This helps you assess risk and understand how policy values may fluctuate with market conditions.
              </p>
            </div>
          </Card>
          {/* Interest Rate Scenarios - Four Main Options */}
          <Card className="mb-8">
           {/* <h2 className="text-xl font-bold mb-6 text-black">Interest Rate Scenarios</h2> */}
            <div className="space-y-6">
              {/* Unified Options Container */}
              <div className="bg-white p-6 rounded-lg border-2 border-gray-300 space-y-6">
                {/* Radio group for Current and Guaranteed rates */}
                <div className="flex flex-col gap-4">
                  <label className="flex items-center text-lg font-semibold text-black">
                    <input
                      type="checkbox"
                      checked={userDefinedRatesData.selectedTypes.current}
                      onChange={() => {
                        setUserDefinedRatesData(prev => ({
                          ...prev,
                          selectedTypes: { ...prev.selectedTypes, current: !prev.selectedTypes.current, guaranteed: false }
                        }));
                      }}
                      className="mr-2"
                    />
                    Current interest/crediting rate: {getCurrentInterestRate()}
                  </label>
                  <label className="flex items-center text-lg font-semibold text-black">
                    <input
                      type="checkbox"
                      checked={userDefinedRatesData.selectedTypes.guaranteed}
                      onChange={() => {
                        setUserDefinedRatesData(prev => ({
                          ...prev,
                          selectedTypes: { ...prev.selectedTypes, current: false, guaranteed: !prev.selectedTypes.guaranteed }
                        }));
                      }}
                      className="mr-2"
                    />
                    Guaranteed minimum rate: {getGuaranteedMinimumRate()}
                  </label>
                </div>
                {/* Stress scenario rate - Checkbox */}
                <label className="flex items-center text-lg font-semibold text-black">
                  <input
                    type="checkbox"
                    checked={stressRateEnabled}
                    onChange={e => {
                      setStressRateEnabled(e.target.checked);
                      if (!e.target.checked) setStressRate('');
                    }}
                    className="mr-2"
                  />
                  Stress scenario rate
                </label>
                {stressRateEnabled && (
                  <Input
                    value={stressRate}
                    onChange={e => setStressRate(e.target.value)}
                    placeholder="Enter stress scenario rate"
                    className="text-black placeholder-gray-500 w-64"
                  />
                )}
                {/* User-defined interest rate Schedule - Checkbox */}
                <label className="flex items-center text-lg font-semibold text-black">
                  <input
                    type="checkbox"
                    checked={userDefinedRatesData.enabled}
                    onChange={(e) => {
                      setUserDefinedRatesData(prev => ({
                        ...prev,
                        enabled: e.target.checked
                      }));
                    }}
                    className="mr-2"
                  />
                  User-defined interest rate Schedule
                </label>
                {userDefinedRatesData.enabled && (
                  <div className="mt-4 space-y-6">
                    {/* Single Container for Type Selection and Range Controls */}
                    <div className="bg-gray-50 p-6 rounded-lg border-2 border-gray-300">
                      {/* Type Selection Checkboxes - only one can be selected */}
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={userDefinedRatesData.selectedTypes.age}
                            onChange={(e) => setUserDefinedRatesData(prev => ({
                              ...prev,
                              selectedTypes: {
                                current: false,
                                guaranteed: false,
                                age: e.target.checked,
                                policyYear: false,
                                calendarYear: false
                              }
                            }))}
                            className="mr-2"
                          />
                          Age
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={userDefinedRatesData.selectedTypes.policyYear}
                            onChange={(e) => setUserDefinedRatesData(prev => ({
                              ...prev,
                              selectedTypes: {
                                current: false,
                                guaranteed: false,
                                age: false,
                                policyYear: e.target.checked,
                                calendarYear: false
                              }
                            }))}
                            className="mr-2"
                          />
                          Policy Year
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={userDefinedRatesData.selectedTypes.calendarYear}
                            onChange={(e) => setUserDefinedRatesData(prev => ({
                              ...prev,
                              selectedTypes: {
                                current: false,
                                guaranteed: false,
                                age: false,
                                policyYear: false,
                                calendarYear: e.target.checked
                              }
                            }))}
                            className="mr-2"
                          />
                          Calendar Year
                        </label>
                      </div>

                          {/* Age Range Toggle Bars */}
                          {userDefinedRatesData.selectedTypes.age && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-6">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        ageRange: { ...prev.ageRange, start: Math.max(calculateCurrentAge(), prev.ageRange.start - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {userDefinedRatesData.ageRange.start}
                                    </span>
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        ageRange: { ...prev.ageRange, start: Math.min(100, prev.ageRange.start + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">End Age</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        ageRange: { ...prev.ageRange, end: Math.max(prev.ageRange.start, prev.ageRange.end - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {userDefinedRatesData.ageRange.end}
                                    </span>
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        ageRange: { ...prev.ageRange, end: Math.min(100, prev.ageRange.end + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Policy Year Range Toggle Bars */}
                          {userDefinedRatesData.selectedTypes.policyYear && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-6">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        policyYearRange: { ...prev.policyYearRange, start: Math.max(calculateCurrentPolicyYear(), prev.policyYearRange.start - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {userDefinedRatesData.policyYearRange.start}
                                    </span>
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        policyYearRange: { ...prev.policyYearRange, start: Math.min(100, prev.policyYearRange.start + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        policyYearRange: { ...prev.policyYearRange, end: Math.max(prev.policyYearRange.start, prev.policyYearRange.end - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {userDefinedRatesData.policyYearRange.end}
                                    </span>
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        policyYearRange: { ...prev.policyYearRange, end: Math.min(100, prev.policyYearRange.end + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Calendar Year Range Toggle Bars */}
                          {userDefinedRatesData.selectedTypes.calendarYear && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-6">
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        calendarYearRange: { ...prev.calendarYearRange, start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {userDefinedRatesData.calendarYearRange.start}
                                    </span>
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        calendarYearRange: { ...prev.calendarYearRange, start: Math.min(2100, prev.calendarYearRange.start + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                                <div>
                                  <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                                  <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        calendarYearRange: { ...prev.calendarYearRange, end: Math.max(prev.calendarYearRange.start, prev.calendarYearRange.end - 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ◀
                                    </button>
                                    <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                      {userDefinedRatesData.calendarYearRange.end}
                                    </span>
                                    <button
                                      onClick={() => setUserDefinedRatesData(prev => ({
                                        ...prev,
                                        calendarYearRange: { ...prev.calendarYearRange, end: Math.min(2100, prev.calendarYearRange.end + 1) }
                                      }))}
                                      className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                    >
                                      ▶
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                    </div>

                    {/* Buttons Row - moved outside container and above table */}
                    <div className="flex justify-between items-center mt-6 mb-4">
                      <button
                        className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
                      >
                        View Year by Year Details
                      </button>
                      <button
                        onClick={() => setUserDefinedRatesData(prev => ({ ...prev, isEditing: !prev.isEditing }))}
                        className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
                      >
                        {userDefinedRatesData.isEditing ? 'Lock Schedule' : 'Modify Schedule'}
                      </button>
                    </div>

                    {/* Data Table */}
                    <div className="mt-4">
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse border border-gray-300">
                          <thead>
                            <tr className="bg-gray-100">
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Age</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Policy Year</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Calendar Year</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Interest Rate (%)</th>
                            </tr>
                          </thead>
                          <tbody>
                            {userDefinedRatesData.tableData.length === 0 ? (
                              <tr>
                                <td
                                  colSpan={4}
                                  className="border border-gray-300 px-4 py-2 text-center text-gray-500"
                                >
                                  Select year range to populate table
                                </td>
                              </tr>
                            ) : (
                              userDefinedRatesData.tableData.map((row, index) => (
                                <tr key={index}>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.age}</td>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.policyYear}</td>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.calendarYear}</td>
                                  <td className="border border-gray-300 px-4 py-2">
                                    <input
                                      type="number"
                                      value={row.interestRate}
                                      readOnly={!userDefinedRatesData.isEditing}
                                      onChange={(e) => {
                                        if (userDefinedRatesData.isEditing) {
                                          const newTableData = [...userDefinedRatesData.tableData];
                                          newTableData[index].interestRate = parseFloat(e.target.value) || 0;
                                          setUserDefinedRatesData(prev => ({ ...prev, tableData: newTableData }));
                                        }
                                      }}
                                      className={`w-full p-2 border rounded text-black ${
                                        userDefinedRatesData.isEditing
                                          ? 'border-gray-300 bg-white'
                                          : 'border-gray-300 bg-gray-100'
                                      }`}
                                      step="0.1"
                                    />
                                  </td>
                                </tr>
                              ))
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={() => alert('Interest rate scenario configuration saved successfully!')}
              variant="primary"
              className="flex items-center space-x-2 bg-black hover:bg-gray-800 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Interest Rate Illustration</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-black hover:bg-gray-800 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default InterestRatePage;