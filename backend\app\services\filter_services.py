from fastapi import HTTPException
from typing import List, Dict, Any
from app.db.connection import get_connection
from app.models.filter_models import (IllustrationOptionOut,IllustrationQuestionOut,IllustrationTypeOut,FullIllustrationOptionsResponse,Rider,Beneficiary,Transaction,
ScheduleRepayment,Loan,PolicyViewResponse,IllustrationOptionsWithPolicyResponse)

def get_full_illustration_options(policy_id:str) -> FullIllustrationOptionsResponse:
    print("cl: Entered get_full_illustration_options")
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        cursor.execute("""
            SELECT 
                t.ILLUSTRATION_TYPE_ID,
                q.ILLUSTRATION_QUESTION_ID,
                o.ILLUSTRATION_OPTION_ID
            FROM ILLUSTRATION_TYPE_TABLE t
            JOIN ILLUSTRATION_QUESTION_TABLE q 
                ON q.ILLUSTRATION_TYPE_ID = t.ILLUSTRATION_TYPE_ID
            LEFT JOIN ILLUSTRATION_OPTION_TABLE o 
                ON o.ILLUSTRATION_QUESTION_ID = q.ILLUSTRATION_QUESTION_ID
                AND o.ILLUSTRATION_TYPE_ID = t.ILLUSTRATION_TYPE_ID
            ORDER BY 
                t.ILLUSTRATION_TYPE_ID, 
                q.ILLUSTRATION_QUESTION_ID, 
                o.ILLUSTRATION_OPTION_ID
        """)
        rows = cursor.fetchall()
        if not rows:
            raise HTTPException(status_code=404, detail="No illustration data found")

        tree: Dict[int, Dict[str, Any]] = {}

        for row in rows:
            type_id = row["ILLUSTRATION_TYPE_ID"]
            question_id = row["ILLUSTRATION_QUESTION_ID"]
            option_id = row["ILLUSTRATION_OPTION_ID"]

            if type_id not in tree:
                tree[type_id] = {
                    "type_id": type_id,
                    "type_display": "yes",
                    "questions": {}
                }

            if question_id not in tree[type_id]["questions"]:
                tree[type_id]["questions"][question_id] = {
                    "question_id": question_id,
                    "question_display": "yes",
                    "options": []
                }

            if option_id is not None:
                tree[type_id]["questions"][question_id]["options"].append({
                    "option_id": option_id,
                    "selected": False,
                    "option_display": "yes"
                })

        response = FullIllustrationOptionsResponse(
            illustration_options=[
                IllustrationTypeOut(
                    type_id=type_data["type_id"],
                    type_display=type_data["type_display"],
                    questions=[
                        IllustrationQuestionOut(
                            question_id=question_data["question_id"],
                            question_display=question_data["question_display"],
                            options=[
                                IllustrationOptionOut(**opt)
                                for opt in question_data["options"]
                            ]
                        )
                        for question_data in type_data["questions"].values()
                    ]
                )
                for type_data in tree.values()
            ]
        )

        print(f"cl: Full illustration options response: {response}")
        policy = get_policy_details(policy_id)

        # ✅ Return both in a dict
        return {
            "all_illustrations": response,
            "policy_details": policy,
        }

    finally:
        cursor.close()
        conn.close()

def get_policy_details(policy_id: int) -> Dict:
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)

    try:
        # 📌 Main query to fetch policy + related entities
        cursor.execute("""
            SELECT 
    -- POLICY
    p.POLICY_ID, p.CUSTOMER_ID, p.INSURANCE_COMPANY_ID, p.AGENT_ID, p.INSURANCE_PRODUCT_CODE,
    p.POLICY_TYPE, p.POLICY_ISSUED_DATE, p.POLICY_EXPIRY_DATE, p.POLICY_STATUS,
    p.PREMIUM_AMOUNT, p.FACE_AMOUNT, p.LOAN_AMOUNT_DISBURESED,
    p.MINIMUM_INTEREST_RATE_IN_PERCENTAGE, p.GUARANTEED_INTEREST_RATE_IN_PERCENTAGE,
    p.CURRENT_INTEREST_RATE_IN_PERCENTAGE, p.WITHDRAWL_AMOUNT, p.RIDER_APPLICABLE,
    p.POLICY_TERM_YEARS, p.CURRENT_CASH_VALUE,

    -- CUSTOMER
    c.CUSTOMER_FIRST_NAME, c.CUSTOMER_MIDDLE_NAME, c.CUSTOMER_LAST_NAME,
    c.SALUTATION, c.GENDER, c.DATE_OF_BIRTH, c.CONTACT_NUMBER, c.EMAIL,
    c.ADDRESS_LINE_1 AS CUSTOMER_ADDRESS_LINE_1, c.ADDRESS_LINE_2 AS CUSTOMER_ADDRESS_LINE_2,
    c.CITY AS CUSTOMER_CITY, c.STATE AS CUSTOMER_STATE, c.ZIP_CODE AS CUSTOMER_ZIP,
    c.COUNTRY AS CUSTOMER_COUNTRY,

    -- AGENT
    a.AGENT_ID, a.AGENT_CODE, a.SALUTATION AS AGENT_SALUTATION,
    a.AGENT_FIRST_NAME, a.AGENT_MIDDLE_NAME, a.AGENT_LAST_NAME, a.AGENT_NAME,
    a.AGENT_GENDER, a.AGENT_EMAIL, a.AGENT_PHONE_NUMBER,
    a.AGENT_STATE, a.AGENT_STATUS,

    -- PRODUCT
    pr.INSURANCE_PRODUCT_CODE, pr.INSURANCE_PRODUCT_NAME,
    pr.INSURANCE_PRODUCT_LINE_OF_BUSINESS, pr.INSURANCE_PRODUCT_DESCRIPTION,
    pr.INSURANCE_PRODUCT_STATUS, pr.INSURANCE_PRODUCT_EXPIRY_DATE,

    -- COMPANY
    ic.INSURANCE_COMPANY_NAME,
    ic.CONTACT_NUMBER AS COMPANY_PHONE, ic.EMAIL AS COMPANY_EMAIL,
    ic.ADDRESS_LINE_1 AS COMPANY_ADDRESS_LINE_1, ic.ADDRESS_LINE_2 AS COMPANY_ADDRESS_LINE_2,
    ic.CITY AS COMPANY_CITY, ic.STATE AS COMPANY_STATE, ic.ZIP_CODE AS COMPANY_ZIP,
    ic.COUNTRY AS COMPANY_COUNTRY

FROM INS_POLICY p
JOIN INS_CUSTOMER c ON p.CUSTOMER_ID = c.CUSTOMER_ID
JOIN INS_INSURANCE_AGENT a ON p.AGENT_ID = a.AGENT_ID
JOIN INS_INSURANCE_PRODUCT pr ON p.INSURANCE_PRODUCT_CODE = pr.INSURANCE_PRODUCT_CODE
JOIN INS_INSURANCE_COMPANY ic ON p.INSURANCE_COMPANY_ID = ic.INSURANCE_COMPANY_ID
WHERE p.POLICY_ID = %s;
        """, (policy_id,))
        policy = cursor.fetchone()

        if not policy:
            raise Exception("Policy not found.")

        # 🔄 Fetch nested data
        def fetch_related(query, params):
            cursor.execute(query, params)
            return cursor.fetchall()

        policy["riders"] = fetch_related("SELECT * FROM INS_RIDER WHERE POLICY_ID = %s", (policy_id,))
        policy["beneficiaries"] = fetch_related("SELECT * FROM INS_BENEFICIARY WHERE POLICY_ID = %s", (policy_id,))
        policy["transactions"] = fetch_related("SELECT * FROM INS_TRANSACTION WHERE POLICY_ID = %s", (policy_id,))
        policy["loans"] = []

        cursor.execute("SELECT * FROM INS_LOAN WHERE POLICY_ID = %s", (policy_id,))
        loans = cursor.fetchall()

        for loan in loans:
            cursor.execute("SELECT * FROM INS_SCHEDULE_REPAYMENT WHERE LOAN_ID = %s", (loan["LOAN_ID"],))
            loan["repayments"] = cursor.fetchall()
            policy["loans"].append(loan)

        return policy

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {e}")

    finally:
        cursor.close()
        conn.close()
def options_applicable_for_product(product_code: str, policy: dict) -> Dict:
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)

    # Step 1: Fetch product config
    cursor.execute("SELECT * FROM PRODUCT_CONFIG WHERE PRODUCT_CODE = %s", (product_code,))
    product = cursor.fetchone()

    if not product:
        return {"count": 0, "data": []}

    response = []

    # Step 2: Fetch all illustration types
    cursor.execute("SELECT ILLUSTRATION_TYPE_ID, TYPE_TEXT FROM ILLUSTRATION_TYPE_TABLE")
    type_rows = cursor.fetchall()

    for type_row in type_rows:
        type_id = type_row["ILLUSTRATION_TYPE_ID"]
        type_text = type_row["TYPE_TEXT"]  # e.g., "AS_IS", "PREMIUM_BASED", etc.

        column_name = type_text  # should match PRODUCT_CONFIG column like "AS_IS"

        if column_name not in product:
            print(f"⚠️ Warning: Column '{column_name}' not found in PRODUCT_CONFIG.")
            continue

        display_flag = "Y" if product.get(column_name, "").upper() == "YES" else "NO"

        ill_type_block = {
            "ILL_type_id": type_id,
            "Display_flag": display_flag,
            "children": []
        }
        cursor.execute("""
            SELECT ILLUSTRATION_QUESTION_ID FROM ILLUSTRATION_QUESTION_TABLE
            WHERE ILLUSTRATION_TYPE_ID = %s
        """, (type_id,))
        questions = cursor.fetchall()

        for q in questions:
            q_id = q["ILLUSTRATION_QUESTION_ID"]
            question_block = {
                "Ill_question_id": q_id,
                "Display_flag": display_flag,
                "children": []
            }

            # Always fetch options regardless of display_flag
            cursor.execute("""
                SELECT ILLUSTRATION_OPTION_ID FROM ILLUSTRATION_OPTION_TABLE
                WHERE ILLUSTRATION_QUESTION_ID = %s
            """, (q_id,))
            options = cursor.fetchall()

            for opt in options:
                question_block["children"].append({
                    "Ill_option_id": opt["ILLUSTRATION_OPTION_ID"],
                    "Display_flag": display_flag
                })

            ill_type_block["children"].append(question_block)


        # if display_flag == "Y" :
        #     # Get questions
        #     cursor.execute("""
        #         SELECT ILLUSTRATION_QUESTION_ID FROM ILLUSTRATION_QUESTION_TABLE
        #         WHERE ILLUSTRATION_TYPE_ID = %s
        #     """, (type_id,))
        #     questions = cursor.fetchall()

        #     for q in questions:
        #         q_id = q["ILLUSTRATION_QUESTION_ID"]
        #         question_block = {
        #             "Ill_question_id": q_id,
        #             "Display_flag": display_flag,
        #             "children": []
        #         }

        #         # Get options
        #         cursor.execute("""
        #             SELECT ILLUSTRATION_OPTION_ID FROM ILLUSTRATION_OPTION_TABLE
        #             WHERE ILLUSTRATION_QUESTION_ID = %s
        #         """, (q_id,))
        #         options = cursor.fetchall()

        #         for opt in options:
        #             question_block["children"].append({
        #                 "Ill_option_id": opt["ILLUSTRATION_OPTION_ID"],
        #                 "Display_flag": display_flag
        #             })

        #         ill_type_block["children"].append(question_block)

        response.append(ill_type_block)

    # Step 3: Apply business rules (filter)
    filtered_response = filter_policy_conditions(policy, response)

    cursor.close()
    conn.close()

    return {
        "count": len(filtered_response),
        "data": filtered_response
    }


def filter_policy_conditions(policy: dict, response: List[Dict]) -> List[Dict]:

    policy_id = policy.get("POLICY_ID")
    if not policy_id:
        return response  # Can't check anything without policy_id
    print(response)
    conn = get_connection()
    cursor = conn.cursor(dictionary=True)

    # Step 1: Check if loan exists
    cursor.execute("""
        SELECT LOAN_AMOUNT FROM INS_LOAN
        WHERE POLICY_ID = %s
    """, (policy_id,))
    loan_rows = cursor.fetchall()

    has_loan = any(row["LOAN_AMOUNT"] > 0 for row in loan_rows)
    # Step 2: Loop through each illustration type
    for type_block in response:
        ill_type_id = type_block["ILL_type_id"]

        # Get TYPE_TEXT for current ILLUSTRATION_TYPE_ID
        cursor.execute("""
            SELECT TYPE_TEXT FROM ILLUSTRATION_TYPE_TABLE
            WHERE ILLUSTRATION_TYPE_ID = %s
        """, (ill_type_id,))
        type_row = cursor.fetchone()

        if type_row:
            type_text = type_row["TYPE_TEXT"].strip().upper()

            # Only apply special logic for LOAN_REPAYMENT
            if type_text == "LOAN_REPAYMENT":
                flag = "Y" if has_loan else "NO"
                type_block["Display_flag"] = flag
                print(">>> TYPE:", ill_type_id, " | has_loan:", has_loan)
                print(">>> Children:", type_block.get("children"))

                # Apply same flag to all child questions and options
                # for question in type_block.get("children", []):
                #     question["Display_flag"] = flag
                #     for option in question.get("children", []):
                #         option["Display_flag"] = flag
                for question in type_block.get("children", []):
                    question["Display_flag"] = flag
                    print("  Updated question:", question.get("Ill_question_id"), "with flag:", flag)

                    

                    for option in question.get("children", []):
                        option["Display_flag"] = flag
                        print("    Updated option:", option.get("Ill_option_id"), "with flag:", flag)


            else:
                # For all other types, always set "NO" (as per your request)
                pass
                # type_block["Display_flag"] = "NO"
                # for question in type_block.get("children", []):
                #     question["Display_flag"] = "NO"
                #     for option in question.get("children", []):
                #         option["Display_flag"] = "NO"

    cursor.close()
    conn.close()
    return response
