from fastapi import APIRouter, HTTPException
from app.models.get_illustration_scenario_models import (
    GetIllustrationScenarioRequest,
    GetIllustrationScenarioResponse
)
from app.services.get_illustration_scenario_services import get_illustration_scenario_service, fetch_all_disclosures

get_scenario = APIRouter()

@get_scenario.post("/get_selected_scenario", response_model=GetIllustrationScenarioResponse)
def get_illustration_scenario(request: GetIllustrationScenarioRequest):
    try:
        return get_illustration_scenario_service(
            policy_id=request.policy_id,
            scenario_id=request.scenario_id
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@get_scenario.get("/disclosures", summary="Get all disclosures")
def get_disclosures():
    try:
        disclosures = fetch_all_disclosures()
        return {
            "success": True,
            "data": disclosures,
            "message": "Disclosure data fetched successfully"
        }
    except HTTPException as e:
        return {
            "success": False,
            "data": [],
            "message": str(e.detail)
        }
    except Exception as e:
        return {
            "success": False,
            "data": [],
            "message": f"Error fetching disclosures: {str(e)}"
        }