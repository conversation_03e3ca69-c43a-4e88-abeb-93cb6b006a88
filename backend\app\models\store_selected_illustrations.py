
from tkinter import NO, YES
from pydantic import BaseModel, Field
from typing import List, Literal, Optional

class IllustrationScheduleObject(BaseModel):
    age:Optional[int]
    policy_year:Optional[int]
    current_year:Optional[int]
    face_amount:Optional[float]
    premium_amount:Optional[float]
    coverage_options:Optional[str]
    loan_amount:Optional[float]
    surrender_amount:Optional[float]
    loan_repayment_amount:Optional[float]
    illustration_interest_rate:Optional[float]


class SelectedOption(BaseModel):
    policy_id:int
    illustration_type_id: int
    illustration_question_id: int
    illustration_option_id: Optional[int]=Field(None, description="Option_id if it exixts")
    illustration_starting_age:Optional[int]
    illustration_ending_age:Optional[int]
    new_face_amount:Optional[float]
    new_coverage_option:Optional[str]
    new_premium_amount:Optional[float]
    new_loan_amount:Optional[float]
    new_loan_repayment_amount:Optional[float]
    current_interest_rate:Optional[float]
    guaranteed_interest_rate:Optional[float]
    illustration_interest_rate:Optional[float]
    surrender_amount:Optional[float]
    RETIREMENT_AGE_GOAL:Optional[int]
    is_schedule: Optional[Literal['YES', 'NO']] = Field(default='NO')
    schedule_object:Optional[list[IllustrationScheduleObject]]
    value:Optional[str]=None
    
class SelectedOptionsRequest(BaseModel):
    selected_options: List[SelectedOption]

class StoreSelectedOptionsResponse(BaseModel):
    status: str
