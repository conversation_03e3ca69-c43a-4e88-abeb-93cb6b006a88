from fastapi import APIRouter, HTTPException
# from app.models.store_selected_illustrations import StoreIllustrationOptionsRequest, StoreIllustrationOptionsResponse
from app.models.store_selected_illustrations import SelectedOptionsRequest, StoreSelectedOptionsResponse
from app.services.store_selected_illustrations_services import store_selected_options

selected_illustrations_routes = APIRouter()
@selected_illustrations_routes.post("/api/illustration/store_options", response_model=StoreSelectedOptionsResponse)
def store_selected_options_route(request: SelectedOptionsRequest):  # ✅ use a different name
    try:
        result = store_selected_options(request)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))