from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import List, Union,Dict
from app.services import Policy_View_services
import requests

router = APIRouter()
@router.get("/insurer/view")
def view_policy_insurer(policy_id: int):
    try:
        print(f"🔍 Fetching policy details for ID: {policy_id}")
        policy_data = Policy_View_services.fetch_full_policy_details(policy_id)
        return policy_data
    except Exception as e:
        print("❌ Error fetching policy:", e)
        return JSONResponse(status_code=404, content={"error": str(e)})


# ✅ 4️⃣ Store policy details from UI into ILL tables
@router.post("/ui/viewed_policy")
def store_viewed_policy_ui(policy_id: int = Query(...)):
    try:
        print(f"📦 UI requested to store policy ID {policy_id} in ILL tables")

        # 🔁 Call insurer's GET API to fetch full policy details
        insurer_api_url = f"http://127.0.0.1:8000/View_Policy/insurer/view?policy_id={policy_id}"
        response = requests.get(insurer_api_url)

        if response.status_code != 200:
            print(f"❌ Error calling insurer API: {response.text}")
            return JSONResponse(status_code=response.status_code, content={"error": "Could not fetch policy from insurer"})

        policy_data = response.json()
        print(f"Fetched policy data: {policy_data}")

        # ✅ Now store this JSON in the ILL tables
        Policy_View_services.store_policy_in_ill_tables(policy_data)

        return {"status": "success",
            "message": f"Policy {policy_id} stored in ILL tables.",
            "data": policy_data}

    except Exception as e:
        print("❌ Error during store_viewed_policy_ui:", e)
        return JSONResponse(status_code=500, content={"error": str(e)})