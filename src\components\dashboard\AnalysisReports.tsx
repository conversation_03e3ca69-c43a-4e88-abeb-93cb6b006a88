import React, { useState } from 'react';
import { FileText, Eye, Calendar, User, Send } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';

const AnalysisReports: React.FC = () => {
  const { selectedCustomerData } = useDashboard();
  const [deliveryMethod, setDeliveryMethod] = useState<string[]>([]);
  const [isSending, setIsSending] = useState(false);

  // Sample data for generated reports
  const sampleReports = [
    {
      id: 1,
      scenarioName: 'Scenario A - High Growth',
      generatedBy: '<PERSON>',
      generatedDate: '2024-01-15',
      status: 'Completed',
      fileSize: '2.4 MB',
      type: 'PDF'
    },
    {
      id: 2,
      scenarioName: 'Scenario B - Conservative',
      generatedBy: '<PERSON>',
      generatedDate: '2024-01-14',
      status: 'Completed',
      fileSize: '1.8 MB',
      type: 'PDF'
    },
    {
      id: 3,
      scenarioName: 'Scenario C - Balanced',
      generatedBy: '<PERSON>',
      generatedDate: '2024-01-13',
      status: 'Processing',
      fileSize: '--',
      type: 'PDF'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'Processing':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  };





  const handleSendDocuments = async () => {
    setIsSending(true);
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert('Documents sent successfully!');
    } catch (error) {
      alert('Failed to send documents. Please try again.');
    } finally {
      setIsSending(false);
    }
  };



  return (
    <div className="space-y-6">
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <FileText className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Generated Reports
              </h2>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {sampleReports.length} reports available
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">
                    Scenario Name
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">
                    Generated By
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">
                    Date
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">
                    Status
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">
                    File Size
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-gray-100">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {sampleReports.map((report) => (
                  <tr key={report.id} className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="py-4 px-4">
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {report.scenarioName}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {report.type} Document
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <User className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-700 dark:text-gray-300">
                          {report.generatedBy}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-700 dark:text-gray-300">
                          {report.generatedDate}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(report.status)}`}>
                        {report.status}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-gray-700 dark:text-gray-300">
                      {report.fileSize}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <button className="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors">
                          <Eye className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {sampleReports.length === 0 && (
            <div className="text-center py-12">
              <FileText className="w-20 h-20 text-gray-300 dark:text-gray-600 mx-auto mb-6" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Reports Available</h3>
              <p className="text-gray-500 dark:text-gray-400">
                Generate reports from your selected scenarios to see them here.
              </p>
            </div>
          )}
        </div>
      </Card>

      {/* Document Delivery Section */}
      {/* Allow selecting multiple delivery options */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Send className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Document Delivery
          </h2>
        </div>
          </div>

          {/* Delivery Options */}
          <div className="space-y-4">
        {/* 1. Address by Post */}
        <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-center space-x-3">
            <input
          type="radio"
          name="deliveryMethod_post"
          value="post"
          checked={deliveryMethod.includes('post')}
          onClick={() => {
            setDeliveryMethod(prev => 
              prev.includes('post') ? prev.filter(m => m !== 'post') : [...prev, 'post']
            );
          }}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span className="font-medium text-gray-900 dark:text-gray-100">
          Address by Post
            </span>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {selectedCustomerData?.details?.Address || 'No address available'}
          </div>
        </div>

        {/* 2. Share through Email */}
        <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-center space-x-3">
            <input
          type="radio"
          name="deliveryMethod_email"
          value="email"
          checked={deliveryMethod.includes('email')}
          onClick={() => {
            setDeliveryMethod(prev => 
              prev.includes('email') ? prev.filter(m => m !== 'email') : [...prev, 'email']
            );
          }}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span className="font-medium text-gray-900 dark:text-gray-100">
          Share through Email
            </span>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {selectedCustomerData?.details?.Email || 'No email available'}
          </div>
        </div>

        {/* 3. Share through WhatsApp */}
        <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-center space-x-3">
            <input
          type="radio"
          name="deliveryMethod_whatsapp"
          value="whatsapp"
          checked={deliveryMethod.includes('whatsapp')}
          onClick={() => {
            setDeliveryMethod(prev => 
              prev.includes('whatsapp') ? prev.filter(m => m !== 'whatsapp') : [...prev, 'whatsapp']
            );
          }}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span className="font-medium text-gray-900 dark:text-gray-100">
          Share through WhatsApp
            </span>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {selectedCustomerData?.details?.Phone || 'No phone number available'}
          </div>
        </div>
          </div>

          {/* Send Button */}
          <div className="mt-6">
        <Button
          onClick={handleSendDocuments}
          disabled={isSending || deliveryMethod.length === 0}
          className="w-full"
        >
          {isSending ? (
            <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          <span>Sending...</span>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
          <Send className="w-4 h-4" />
          <span>Send Documents</span>
            </div>
          )}
        </Button>
          </div>
        </div>
      </Card>
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <Send className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Document Delivery
              </h2>
            </div>
          </div>
        </div>
      </Card>
 </div>
  );
};

export default AnalysisReports;
