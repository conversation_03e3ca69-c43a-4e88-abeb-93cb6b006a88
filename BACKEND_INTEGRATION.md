# Backend Integration for Selected Scenarios

## Overview
The Selected Scenarios component has been **completely updated** to fetch ALL data from the backend API. **All hardcoded/dummy data has been removed** while maintaining the exact same UI style and functionality. This document explains the implementation and how to configure the backend URL.

## ✅ **Hardcoded Data Removed**
- ❌ **Scenario table data** - Now fetched from `/scenario-details/{scenarioId}` endpoint
- ❌ **Disclosure information** - Now fetched from `/disclosure-data/{policyId}` endpoint
- ✅ **UI styling and layout** - Preserved exactly as before
- ✅ **Chart functionality** - Maintained with backend data
- ✅ **Loading states** - Added for better user experience
- ✅ **Error handling** - Added with retry functionality

## Changes Made

### 1. New Service Created
- **File**: `src/services/scenarioService.ts`
- **Purpose**: Handles API calls to fetch scenario details from the backend
- **Methods**:
  - `fetchScenarioDetails()` - GET request to fetch scenario data
  - `fetchScenarioDetailsPost()` - POST request alternative (if needed)

### 2. Component Updates
- **File**: `src/components/dashboard/SelectedScenarios.tsx`
- **Changes**:
  - Removed dummy data generation (`generateTableData` function)
  - Added backend data fetching with `useEffect` hook
  - Added loading and error states
  - Added retry functionality
  - Shows backend URL in the UI for debugging

### 3. Data Flow
1. User clicks on a scenario card
2. Component triggers `fetchScenarioDetails()` API call
3. Backend returns scenario data in JSON format
4. Data is transformed and displayed in tables and charts
5. Loading states and error handling provide user feedback

## Backend API Endpoints

### 1. Scenario Details Endpoint
```
GET /scenario-details/{scenarioId}
```

#### Request Parameters
- `scenarioId`: The ID of the selected scenario
- `policyId`: The policy ID from selected policy data
- `customerId`: The customer ID from selected customer data
- `category`: The scenario category (face-amount, premium, etc.)

#### Expected Response Format
```json
{
  "success": true,
  "data": [
    {
      "policy_year": 2025,
      "end_of_age": 40,
      "planned_premium": 10000,
      "net_outlay": 5000,
      "net_surrender_value": 50000,
      "net_death_benefit": 250000
    },
    // ... more rows
  ],
  "message": "Scenario details fetched successfully"
}
```

### 2. Disclosure Data Endpoint
```
GET /disclosure-data/{policyId}
```

#### Request Parameters
- `policyId`: The policy ID from selected policy data
- `customerId`: The customer ID from selected customer data
- `policyType`: The policy type (optional)

#### Expected Response Format
```json
{
  "success": true,
  "data": [
    {
      "se_no": 1,
      "type": "About In-Force Illustration",
      "disclosure": "This illustration is intended to assist you in understanding how your policy may perform over time..."
    },
    {
      "se_no": 2,
      "type": "Premiums You Pay",
      "disclosure": "Your policy accepts flexible premiums. Paying minimum required premiums keeps the policy active..."
    },
    // ... more disclosure items
  ],
  "message": "Disclosure data fetched successfully"
}
```

### Alternative Response Formats Supported
The service can handle various backend response structures:
- `{ data: [...] }`
- `{ results: [...] }`
- `{ scenarios: [...] }`
- Direct array `[...]`

## Configuration

### Backend URL
Set the backend URL using environment variables:

```bash
# .env file
VITE_API_BASE_URL=http://localhost:8000
```

Or for production:
```bash
VITE_API_BASE_URL=https://your-backend-api.com
```

### Default Fallback
If no environment variable is set, it defaults to `http://localhost:8000`

## Error Handling

### Frontend Error States
1. **Loading State**: Shows spinner while fetching data
2. **Error State**: Shows error message with retry button
3. **No Data State**: Shows message when backend returns empty data
4. **Network Error**: Shows connection error with backend URL

### Backend Error Responses
The service handles:
- HTTP errors (404, 500, etc.)
- Network timeouts
- Invalid JSON responses
- Missing or malformed data

## Testing the Integration

### 1. Check Browser Console
Look for these log messages:
- `🚀 Fetching scenario details for:` - Request details
- `🔍 Making API call to:` - API endpoint URL
- `✅ Backend response:` - Raw backend data
- `✅ Scenario data loaded:` - Transformed data

### 2. Check Network Tab
- Verify the API call is made to the correct endpoint
- Check request/response headers and data
- Verify HTTP status codes

### 3. UI Indicators
- Backend URL is displayed in the Data Visualization Options card
- Loading spinner appears when fetching data
- Error messages show if backend is unavailable
- Retry button appears on errors

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Ensure backend allows requests from frontend domain
   - Add proper CORS headers to backend

2. **404 Not Found**
   - Verify the endpoint exists on backend
   - Check if scenarioId parameter is correct

3. **Data Format Issues**
   - Check backend response format matches expected structure
   - Verify field names (snake_case vs camelCase)

4. **Environment Variables**
   - Ensure `.env` file is in project root
   - Restart development server after changing env vars
   - Check if `VITE_` prefix is used correctly

### Debug Steps
1. Open browser developer tools
2. Go to Console tab to see log messages
3. Go to Network tab to see API requests
4. Check the backend URL shown in the UI
5. Use the Retry button to test error recovery

## Next Steps

1. **Backend Implementation**: Implement the `/scenario-details/{scenarioId}` endpoint
2. **Authentication**: Add authentication headers if required
3. **Caching**: Consider adding response caching for better performance
4. **Real-time Updates**: Consider WebSocket integration for live data updates
