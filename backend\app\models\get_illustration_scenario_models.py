
from datetime import date
from typing import List, Literal, Optional
from pydantic import BaseModel, Field

class GetIllustrationScenarioRequest(BaseModel):
    policy_id:int
    scenario_id:int
class IllustrationOptionOut(BaseModel):
    option_id: int
    option_description: str

class IllustrationQuestionOut(BaseModel):
    question_id: int
    question_description: str
    options: List[IllustrationOptionOut]

class IllustrationTypeOut(BaseModel):
    type_id: int
    type_description: str
    questions: List[IllustrationQuestionOut]

class IllustrationObject(BaseModel):
    policy_id:int
    scenario_id:int
    illustration_options: List[IllustrationTypeOut]

class GetIllustrationScenarioResponse(BaseModel):
    illustration:List[IllustrationObject]
    policy_id:int
    scenario_id:int
    illustration_id:int
    date_of_illustration:date
    illustration_type_id:int
    illustration_question_id:int
    illustration_option_id:int
    illustration_starting_age:int
    illustration_ending_age:int
    new_face_amount:float
    new_coverage_option:str
    new_premium_amount:float
    new_loan_amount:float
    new_loan_repayment_amount:float
    current_interest_rate:float
    guaranteed_minimum_rate:float
    illustration_interest_rate:float
    surrrender_amount:float
    is_schedule: Optional[Literal['YES', 'NO']] = Field(default='NO')
