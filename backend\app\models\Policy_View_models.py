from datetime import date
from typing import Optional,List
from pydantic import BaseModel, Field

# Response for UI/Insurer View
class Rider(BaseModel):
    RIDER_ID: int
    POLICY_ID: int
    RIDER_NAME: str
    SUM_ASSURED: float
    PREMIUM_AMOUNT: float

class Beneficiary(BaseModel):
    BENEFICIARY_ID: int
    POLICY_ID: int
    NAME: str
    RELATIONSHIP: str
    SHARE_PERCENTAGE: float

class Transaction(BaseModel):
    TRANSACTION_ID: int
    POLICY_ID: int
    TRANSACTION_TYPE: str
    TRANSACTION_DATE: str
    AMOUNT: float

class ScheduleRepayment(BaseModel):
    REPAYMENT_ID: int
    LOAN_ID: int
    DUE_DATE: str
    AMOUNT_DUE: float
    STATUS: str

class Loan(BaseModel):
    LOAN_ID: int
    POLICY_ID: int
    LOAN_AMOUNT: float
    INTEREST_RATE: float
    START_DATE: str
    repayments: Optional[List[ScheduleRepayment]] = []

class PolicyViewResponse(BaseModel):
    POLICY_ID: int
    CUSTOMER_ID: int
    INSURANCE_COMPANY_ID: int
    AGENT_ID: int
    PRODUCT_ID: int
    POLICY_NUMBER: str
    POLICY_START_DATE: str
    POLICY_END_DATE: str
    POLICY_STATUS: str
    PREMIUM_AMOUNT: float
    FACE_AMOUNT: float
    riders: Optional[List[Rider]] = []
    beneficiaries: Optional[List[Beneficiary]] = []
    transactions: Optional[List[Transaction]] = []
    loans: Optional[List[Loan]] = []