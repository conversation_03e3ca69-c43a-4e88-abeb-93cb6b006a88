/**
 * As-Is Illustration Service - Backend API Integration
 * 
 * This service handles saving As-Is illustration data to the database
 * using the backend API endpoint for storing illustration scenarios.
 */

// API Base URL - matches the existing API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

/**
 * Interface for As-Is illustration data to be saved
 */
export interface AsIsIllustrationData {
  policyData: {
    policyNumber: string;
    customerName: string;
    customerId: string;
    policyType: string;
    faceAmount: string;
    annualPremium: string;
    paymentPeriod: string;
    dividendOption: string;
    currentAge: string;
    retirementAge: string;
    lifeExpectancy: string;
  };
  illustrationScenarios: {
    retirementGoalAge: string;
  };
  timestamp: string;
}

/**
 * Interface for the backend API request
 */
interface AsIsApiRequest {
  selected_options: Array<{
    policy_id: number;
    illustration_type_id: number;
    illustration_question_id: number;
    illustration_option_id?: number | null;
    illustration_starting_age?: number | null;
    illustration_ending_age?: number | null;
    new_face_amount?: number | null;
    new_coverage_option?: string | null;
    new_premium_amount?: number | null;
    new_loan_amount?: number | null;
    new_loan_repayment_amount?: number | null;
    current_interest_rate?: number | null;
    guaranteed_interest_rate?: number | null;
    illustration_interest_rate?: number | null;
    surrender_amount?: number | null;
    RETIREMENT_AGE_GOAL?: number | null;
    is_schedule?: 'YES' | 'NO';
    schedule_object?: any[] | null;
    value?: string | null;
  }>;
}

/**
 * Interface for the backend API response
 */
interface AsIsApiResponse {
  status: string;
}

/**
 * Save As-Is illustration data to the database
 * 
 * @param illustrationData - The As-Is illustration data to save
 * @returns Promise<{ success: boolean; message: string; scenarioId?: string }>
 */
export const saveAsIsIllustration = async (
  illustrationData: AsIsIllustrationData
): Promise<{ success: boolean; message: string; scenarioId?: string }> => {
  try {
    console.log('🔍 Saving As-Is illustration to database:', illustrationData);

    // Extract policy ID from policy number (assuming it's numeric)
    const policyId = parseInt(illustrationData.policyData.policyNumber);
    if (isNaN(policyId)) {
      throw new Error('Invalid policy number - must be numeric');
    }

    // Prepare the API request data according to the backend schema
    // For As-Is illustration, only send retirement_goal_age and set all other fields to null
    const apiRequest: AsIsApiRequest = {
      selected_options: [
        {
          policy_id: policyId,
          illustration_type_id: 1, // As-Is illustration type (changed from -1 to valid ID)
          illustration_question_id: 101, // As specified in requirements
          illustration_option_id: null, // No specific option for As-Is
          illustration_starting_age: null, // Don't fill with random values
          illustration_ending_age: null, // Don't fill with random values
          new_face_amount: null, // Don't fill with random values
          new_coverage_option: null, // As-Is doesn't change coverage
          new_premium_amount: null, // Don't fill with random values
          new_loan_amount: null, // As-Is doesn't involve loans
          new_loan_repayment_amount: null,
          current_interest_rate: null, // Will be populated by backend
          guaranteed_interest_rate: null, // Will be populated by backend
          illustration_interest_rate: null, // Will be populated by backend
          surrender_amount: null,
          RETIREMENT_AGE_GOAL: parseInt(illustrationData.illustrationScenarios.retirementGoalAge) || null, // Only this field should be filled
          is_schedule: 'NO', // As-Is is not a schedule-based illustration
          schedule_object: null,
          value: `As-Is illustration for retirement age ${illustrationData.illustrationScenarios.retirementGoalAge}`
        }
      ]
    };

    console.log('📤 Sending API request to:', `${API_BASE_URL}/api/illustration/store_options`);
    console.log('📋 Request payload:', JSON.stringify(apiRequest, null, 2));

    // Make the API call to save the illustration
    const response = await fetch(`${API_BASE_URL}/api/illustration/store_options`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiRequest),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      console.error('❌ Response status:', response.status);
      console.error('❌ Response headers:', response.headers);

      // Try to parse error as JSON for better error messages
      let errorMessage = errorText;
      try {
        const errorJson = JSON.parse(errorText);
        if (errorJson.detail) {
          errorMessage = errorJson.detail;
        }
      } catch (e) {
        // Keep original error text if not JSON
      }

      throw new Error(`API Error (${response.status}): ${errorMessage}`);
    }

    const result: AsIsApiResponse = await response.json();
    console.log('✅ API Response:', result);

    if (result.status === 'SUCCESS') {
      return {
        success: true,
        message: 'As-Is illustration saved successfully to database!',
        scenarioId: 'database-stored' // Backend doesn't return scenario ID in current implementation
      };
    } else {
      throw new Error(`Backend returned status: ${result.status}`);
    }

  } catch (error: any) {
    console.error('❌ Error saving As-Is illustration to database:', error);
    
    // Return a user-friendly error message
    const errorMessage = error.message || 'Unknown error occurred while saving to database';
    return {
      success: false,
      message: `Failed to save As-Is illustration: ${errorMessage}`
    };
  }
};

/**
 * Validate As-Is illustration data before saving
 * 
 * @param illustrationData - The data to validate
 * @returns Array of validation error messages (empty if valid)
 */
export const validateAsIsData = (illustrationData: AsIsIllustrationData): string[] => {
  const errors: string[] = [];

  console.log('🔍 Validating As-Is data:', illustrationData);

  // Validate policy data
  if (!illustrationData.policyData.policyNumber) {
    errors.push('Policy Number is required');
  } else if (isNaN(parseInt(illustrationData.policyData.policyNumber))) {
    errors.push('Policy Number must be numeric');
  }

  if (!illustrationData.policyData.customerName) {
    errors.push('Customer Name is required');
  }

  if (!illustrationData.policyData.customerId) {
    errors.push('Customer ID is required');
  }

  if (!illustrationData.policyData.faceAmount) {
    errors.push('Face Amount is required');
  } else if (isNaN(parseFloat(illustrationData.policyData.faceAmount))) {
    errors.push('Face Amount must be numeric');
  }

  if (!illustrationData.policyData.annualPremium) {
    errors.push('Annual Premium is required');
  } else if (isNaN(parseFloat(illustrationData.policyData.annualPremium))) {
    errors.push('Annual Premium must be numeric');
  }

  if (!illustrationData.policyData.currentAge) {
    errors.push('Current Age is required');
  } else {
    const currentAge = parseInt(illustrationData.policyData.currentAge);
    if (isNaN(currentAge) || currentAge <= 0 || currentAge > 150) {
      errors.push('Current Age must be a valid number between 1 and 150');
    }
  }

  // Validate illustration scenarios
  if (!illustrationData.illustrationScenarios.retirementGoalAge) {
    errors.push('Retirement Goal Age is required');
  } else if (isNaN(parseInt(illustrationData.illustrationScenarios.retirementGoalAge))) {
    errors.push('Retirement Goal Age must be numeric');
  }

  return errors;
};
