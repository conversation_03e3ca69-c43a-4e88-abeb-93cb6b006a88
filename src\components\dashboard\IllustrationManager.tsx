import React, { useState } from 'react';
import { Plus, Download, Save, BarChart3 } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import Notification from '../common/Notification';
import { useDashboard } from '../../contexts/DashboardContext';

const IllustrationManager: React.FC = () => {
  const [scenarioName, setScenarioName] = useState('');
  const [asIsOption, setAsIsOption] = useState('');

  const [configParams, setConfigParams] = useState({
    startYear: '2024',
    endYear: '2074',
    projectionRate: '5.5',
    inflationRate: '2.5',
  });
  const [notification, setNotification] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const { addScenario, setActiveTab } = useDashboard();

  const asIsOptions = [
    { value: 'current-policy', label: 'Current Policy Details' },
    { value: 'base-scenario', label: 'Base Scenario' },
    { value: 'default-assumptions', label: 'Default Assumptions' },
  ];





  const handleGetData = () => {
    // Simulate data retrieval
    console.log('Getting data for:', { asIsOption });
    // Add loading state and show success message
  };

  const handleSaveScenario = async () => {
    if (!asIsOption) {
      setNotification({ message: 'Please select AS-IS option', type: 'error' });
      return;
    }
    setIsSaving(true);
    try {
      const newScenario = {
        id: Date.now().toString(),
        name: scenarioName || `Scenario ${new Date().toLocaleDateString()}`,
        policyId: 'current-policy',
        asIsDetails: asIsOption,
        whatIfOptions: [],
        category: 'premium' as any,
        keyPoints: [
          `Start Year: ${configParams.startYear}`,
          `End Year: ${configParams.endYear}`,
          `Projection Rate: ${configParams.projectionRate}%`,
          `Inflation Rate: ${configParams.inflationRate}%`
        ],
        impact: 'neutral',
        data: {
          configParams,
          createdBy: 'Illustration Manager',
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      await addScenario(newScenario);
      setNotification({ message: 'AS-IS configuration saved successfully and added to Selected Scenarios!', type: 'success' });

      // Reset form
      setScenarioName('');
      setAsIsOption('');
    } catch (error) {
      setNotification({ message: 'Error saving scenario. Please try again.', type: 'error' });
      console.error('Error saving scenario:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleAnalyze = () => {
    setActiveTab('analysis-reports');
  };

  const handleConfigChange = (field: string, value: string) => {
    setConfigParams(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="space-y-6">
      {notification && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification(null)}
        />
      )}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Illustration Manager</h1>
        <p className="text-gray-600">Create and manage policy illustration scenarios.</p>
      </div>

      {/* Scenario Selection */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Scenario Selection</h3>
        
        {/* Scenario Name */}
        <div className="mb-6">
          <Input
            label="Scenario Name"
            value={scenarioName}
            onChange={(e) => setScenarioName(e.target.value)}
            placeholder="Enter a name for this scenario"
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Select
              label="AS-IS (Direct Project Details)"
              value={asIsOption}
              onChange={(e) => setAsIsOption(e.target.value)}
              options={asIsOptions}
            />
          </div>
        </div>
      </Card>

      {/* Configuration */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Configuration Parameters</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <Input
            label="Start Year"
            type="number"
            placeholder="Enter start year"
            value={configParams.startYear}
            onChange={(e) => handleConfigChange('startYear', e.target.value)}
          />
          <Input
            label="End Year"
            type="number"
            placeholder="Enter end year"
            value={configParams.endYear}
            onChange={(e) => handleConfigChange('endYear', e.target.value)}
          />
          <Input
            label="Projection Rate (%)"
            type="number"
            placeholder="Enter projection rate"
            value={configParams.projectionRate}
            onChange={(e) => handleConfigChange('projectionRate', e.target.value)}
          />
          <Input
            label="Inflation Rate (%)"
            type="number"
            placeholder="Enter inflation rate"
            value={configParams.inflationRate}
            onChange={(e) => handleConfigChange('inflationRate', e.target.value)}
          />
        </div>

        <div className="flex flex-wrap gap-4">
          <Button onClick={handleGetData} className="flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>Get Data</span>
          </Button>
          <Button onClick={handleSaveScenario} variant="primary" loading={isSaving} disabled={isSaving} className="flex items-center space-x-2">
            <Save className="w-4 h-4" />
            <span>Save Scenario</span>
          </Button>
          <Button onClick={handleAnalyze} variant="secondary" className="flex items-center space-x-2">
            <BarChart3 className="w-4 h-4" />
            <span>Analyze</span>
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default IllustrationManager;