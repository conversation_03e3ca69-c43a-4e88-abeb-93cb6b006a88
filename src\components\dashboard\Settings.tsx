import React, { useState } from 'react';
import { User, Bell, Shield, Palette, Download, Upload } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import Select from '../common/Select';

const Settings: React.FC = () => {
  const [activeSection, setActiveSection] = useState('profile');
  const [profileData, setProfileData] = useState({
    name: 'John Admin',
    email: '<EMAIL>',
    phone: '+****************',
    organization: 'Insurance Solutions Inc.',
    title: 'Senior Insurance Analyst',
  });

  const [preferences, setPreferences] = useState({
    theme: 'light',
    notifications: true,
    autoSave: true,
    defaultTimeRange: '5-years',
  });

  const sections = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'preferences', label: 'Preferences', icon: Palette },
    { id: 'data', label: 'Data Management', icon: Download },
  ];

  const handleProfileUpdate = (field: string, value: string) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePreferenceUpdate = (field: string, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const renderSection = () => {
    switch (activeSection) {
      case 'profile':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Profile Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Full Name"
                value={profileData.name}
                onChange={(e) => handleProfileUpdate('name', e.target.value)}
              />
              <Input
                label="Email Address"
                type="email"
                value={profileData.email}
                onChange={(e) => handleProfileUpdate('email', e.target.value)}
              />
              <Input
                label="Phone Number"
                value={profileData.phone}
                onChange={(e) => handleProfileUpdate('phone', e.target.value)}
              />
              <Input
                label="Organization"
                value={profileData.organization}
                onChange={(e) => handleProfileUpdate('organization', e.target.value)}
              />
              <Input
                label="Job Title"
                value={profileData.title}
                onChange={(e) => handleProfileUpdate('title', e.target.value)}
                className="md:col-span-2"
              />
            </div>
            <Button>Save Profile</Button>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Notification Settings</h3>
            <div className="space-y-4">
              {[
                { id: 'email', label: 'Email Notifications', desc: 'Receive email updates about policy changes and reports' },
                { id: 'push', label: 'Push Notifications', desc: 'Get instant notifications in your browser' },
                { id: 'reports', label: 'Weekly Reports', desc: 'Receive weekly summary reports via email' },
                { id: 'alerts', label: 'Policy Alerts', desc: 'Get notified about important policy events' },
              ].map((notification) => (
                <div key={notification.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">{notification.label}</h4>
                    <p className="text-sm text-gray-500">{notification.desc}</p>
                  </div>
                  <input
                    type="checkbox"
                    defaultChecked={preferences.notifications}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
              ))}
            </div>
            <Button>Save Notification Settings</Button>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Change Password</h4>
                <div className="space-y-3">
                  <Input type="password" placeholder="Current password" />
                  <Input type="password" placeholder="New password" />
                  <Input type="password" placeholder="Confirm new password" />
                </div>
                <Button className="mt-3">Update Password</Button>
              </div>
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Two-Factor Authentication</h4>
                <p className="text-sm text-gray-500 mb-3">Add an extra layer of security to your account</p>
                <Button variant="outline">Enable 2FA</Button>
              </div>
            </div>
          </div>
        );

      case 'preferences':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Application Preferences</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Theme"
                  value={preferences.theme}
                  onChange={(e) => handlePreferenceUpdate('theme', e.target.value)}
                  options={[
                    { value: 'light', label: 'Light' },
                    { value: 'dark', label: 'Dark' },
                    { value: 'auto', label: 'Auto' },
                  ]}
                />
                <Select
                  label="Default Time Range"
                  value={preferences.defaultTimeRange}
                  onChange={(e) => handlePreferenceUpdate('defaultTimeRange', e.target.value)}
                  options={[
                    { value: '1-year', label: '1 Year' },
                    { value: '5-years', label: '5 Years' },
                    { value: '10-years', label: '10 Years' },
                    { value: '20-years', label: '20 Years' },
                  ]}
                />
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900">Auto-save</h4>
                    <p className="text-sm text-gray-500">Automatically save your work</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={preferences.autoSave}
                    onChange={(e) => handlePreferenceUpdate('autoSave', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </div>
              </div>
            </div>
            <Button>Save Preferences</Button>
          </div>
        );

      case 'data':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Data Management</h3>
            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Export Data</h4>
                <p className="text-sm text-gray-500 mb-3">Download your policy data and scenarios</p>
                <div className="flex space-x-3">
                  <Button variant="outline" className="flex items-center space-x-2">
                    <Download className="w-4 h-4" />
                    <span>Export All Data</span>
                  </Button>
                  <Button variant="outline" className="flex items-center space-x-2">
                    <Download className="w-4 h-4" />
                    <span>Export Scenarios</span>
                  </Button>
                </div>
              </div>
              <div className="p-4 border border-gray-200 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Import Data</h4>
                <p className="text-sm text-gray-500 mb-3">Upload policy data from external sources</p>
                <Button variant="outline" className="flex items-center space-x-2">
                  <Upload className="w-4 h-4" />
                  <span>Import Data</span>
                </Button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Settings Navigation */}
        <div className="w-full lg:w-64">
          <Card>
            <nav className="space-y-2">
              {sections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeSection === section.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{section.label}</span>
                  </button>
                );
              })}
            </nav>
          </Card>
        </div>

        {/* Settings Content */}
        <div className="flex-1">
          <Card>
            {renderSection()}
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Settings;